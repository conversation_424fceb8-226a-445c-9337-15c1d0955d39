package repository

import (
	"context"
	"time"

	"github.com/chessticize/chessticize-server/internal/models"
	"gorm.io/gorm"
)

type UserLearningDailyStatsRepository struct {
	db *gorm.DB
}

func NewUserLearningDailyStatsRepository(db *gorm.DB) IUserLearningDailyStatsRepository {
	return &UserLearningDailyStatsRepository{db: db}
}

// GetByUserIDAndDate retrieves stats for a specific user and date
func (r *UserLearningDailyStatsRepository) GetByUserIDAndDate(ctx context.Context, userID string, date time.Time) (*models.UserLearningDailyStats, error) {
	var stats models.UserLearningDailyStats

	// Truncate to date only (remove time component)
	dateOnly := date.Truncate(24 * time.Hour)

	err := r.db.WithContext(ctx).
		Where("user_id = ? AND date = ?", userID, dateOnly).
		First(&stats).Error

	if err != nil {
		return nil, err
	}

	return &stats, nil
}

// CreateOrUpdate creates or updates daily learning stats
func (r *UserLearningDailyStatsRepository) CreateOrUpdate(ctx context.Context, stats *models.UserLearningDailyStats) error {
	// Ensure date is truncated to date only
	stats.Date = stats.Date.Truncate(24 * time.Hour)

	// Try to find existing record
	var existing models.UserLearningDailyStats
	err := r.db.WithContext(ctx).
		Where("user_id = ? AND date = ?", stats.UserID, stats.Date).
		First(&existing).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			// Create new record
			now := time.Now()
			stats.CreatedAt = now
			stats.UpdatedAt = now
			return r.db.WithContext(ctx).Create(stats).Error
		}
		return err
	}

	// Update existing record
	stats.ID = existing.ID
	stats.CreatedAt = existing.CreatedAt
	stats.UpdatedAt = time.Now()
	return r.db.WithContext(ctx).Save(stats).Error
}

// IncrementLearningAttempt increments learning attempt counters for a specific date
func (r *UserLearningDailyStatsRepository) IncrementLearningAttempt(ctx context.Context, userID string, date time.Time, wasCorrect bool, attemptType string, timeSpent int) error {
	dateOnly := date.Truncate(24 * time.Hour)

	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// Get or create stats for the date
		var stats models.UserLearningDailyStats
		err := tx.Where("user_id = ? AND date = ?", userID, dateOnly).First(&stats).Error

		if err != nil {
			if err == gorm.ErrRecordNotFound {
				// Create new stats record
				stats = models.UserLearningDailyStats{
					UserID: userID,
					Date:   dateOnly,
				}
			} else {
				return err
			}
		}

		// Update counters
		stats.LearningAttempts++
		stats.TotalTimeSpent += timeSpent

		if wasCorrect {
			stats.LearningCorrect++
		}

		// Update attempt type specific counters
		if attemptType == "regular" {
			stats.RegularRetries++
		} else if attemptType == "arrow_duel" {
			stats.ArrowDuelRetries++
		}

		// Save or create the record
		if stats.ID == "" {
			now := time.Now()
			stats.CreatedAt = now
			stats.UpdatedAt = now
			return tx.Create(&stats).Error
		} else {
			stats.UpdatedAt = time.Now()
			return tx.Save(&stats).Error
		}
	})
}

// IncrementPuzzlesMastered increments the puzzles mastered counter for a specific date
func (r *UserLearningDailyStatsRepository) IncrementPuzzlesMastered(ctx context.Context, userID string, date time.Time) error {
	dateOnly := date.Truncate(24 * time.Hour)

	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// Get or create stats for the date
		var stats models.UserLearningDailyStats
		err := tx.Where("user_id = ? AND date = ?", userID, dateOnly).First(&stats).Error

		if err != nil {
			if err == gorm.ErrRecordNotFound {
				// Create new stats record
				stats = models.UserLearningDailyStats{
					UserID:          userID,
					Date:            dateOnly,
					PuzzlesMastered: 1,
				}
				now := time.Now()
				stats.CreatedAt = now
				stats.UpdatedAt = now
				return tx.Create(&stats).Error
			} else {
				return err
			}
		}

		// Update counter
		stats.PuzzlesMastered++
		stats.UpdatedAt = time.Now()
		return tx.Save(&stats).Error
	})
}

// GetWeeklyStats retrieves daily stats for a week starting from the given date
func (r *UserLearningDailyStatsRepository) GetWeeklyStats(ctx context.Context, userID string, startDate time.Time) ([]models.UserLearningDailyStats, error) {
	startDateOnly := startDate.Truncate(24 * time.Hour)
	endDate := startDateOnly.Add(7 * 24 * time.Hour)

	var stats []models.UserLearningDailyStats
	err := r.db.WithContext(ctx).
		Where("user_id = ? AND date >= ? AND date < ?", userID, startDateOnly, endDate).
		Order("date ASC").
		Find(&stats).Error

	return stats, err
}
