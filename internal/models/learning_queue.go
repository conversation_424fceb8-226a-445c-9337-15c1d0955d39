package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// LearningQueueEntry represents a failed Lichess puzzle in the user's learning queue
type LearningQueueEntry struct {
	ID                 string    `gorm:"type:varchar(36);primarykey" json:"id"`
	UserID             string    `gorm:"type:varchar(36);not null;index:idx_learning_queue_user_due,priority:1;uniqueIndex:idx_learning_queue_user_puzzle,priority:1;index:idx_learning_queue_user_type_due,priority:1" json:"user_id"`
	LichessPuzzleID    string    `gorm:"type:varchar(10);not null;uniqueIndex:idx_learning_queue_user_puzzle,priority:2" json:"lichess_puzzle_id"`
	FailedAttemptType  string    `gorm:"type:varchar(20);not null;index:idx_learning_queue_user_type_due,priority:2" json:"failed_attempt_type"` // "regular" or "arrow_duel"
	DueAt              time.Time `gorm:"not null;index:idx_learning_queue_user_due,priority:2;index:idx_learning_queue_user_type_due,priority:3" json:"due_at"`
	AttemptsSinceAdded int       `gorm:"not null;default:0" json:"attempts_since_added"`
	ConsecutiveCorrect int       `gorm:"not null;default:0" json:"consecutive_correct"`
	OriginalSprintID   string    `gorm:"type:varchar(36);not null" json:"original_sprint_id"` // Track which sprint the failure came from
	CreatedAt          time.Time `gorm:"not null" json:"created_at"`
	UpdatedAt          time.Time `gorm:"not null" json:"updated_at"`

	// Relationships
	LichessPuzzle LichessPuzzle `gorm:"foreignKey:LichessPuzzleID;constraint:OnDelete:CASCADE" json:"-"`
	User          User          `gorm:"foreignKey:UserID;constraint:OnDelete:CASCADE" json:"-"`
	Sprint        Sprint        `gorm:"foreignKey:OriginalSprintID;constraint:OnDelete:CASCADE" json:"-"`
}

// TableName returns the table name for LearningQueueEntry
func (LearningQueueEntry) TableName() string {
	return "learning_queue"
}

// BeforeCreate sets the ID if not already set
func (lq *LearningQueueEntry) BeforeCreate(tx *gorm.DB) error {
	if lq.ID == "" {
		lq.ID = uuid.New().String()
	}
	return nil
}

// LearningQueueItem represents a learning queue entry with puzzle data for API responses
type LearningQueueItem struct {
	QueueID            string         `json:"queue_id"`
	LichessPuzzleID    string         `json:"lichess_puzzle_id"`
	FailedAttemptType  string         `json:"failed_attempt_type"`
	DueAt              time.Time      `json:"due_at"`
	AttemptsSinceAdded int            `json:"attempts_since_added"`
	ConsecutiveCorrect int            `json:"consecutive_correct"`
	OriginalSprintID   string         `json:"original_sprint_id"`
	PuzzleData         *LichessPuzzle `json:"puzzle_data"`
}

// LearningQueueStats represents statistics about a user's learning queue
type LearningQueueStats struct {
	TotalQueued         int64   `json:"total_queued"`
	DueToday            int64   `json:"due_today"`
	RegularPuzzles      int64   `json:"regular_puzzles"`
	ArrowDuelPuzzles    int64   `json:"arrow_duel_puzzles"`
	DailyRetriesToday   int64   `json:"daily_retries_today"`
	DailyRetriesThisWeek int64  `json:"daily_retries_this_week"`
	MasteryRate         float64 `json:"mastery_rate"`
}

// UserLearningDailyStats tracks daily learning activity for analytics and motivation
type UserLearningDailyStats struct {
	ID               string    `gorm:"type:varchar(36);primarykey" json:"id"`
	UserID           string    `gorm:"type:varchar(36);not null;uniqueIndex:idx_user_learning_daily_stats_user_date,priority:1" json:"user_id"`
	Date             time.Time `gorm:"type:date;not null;uniqueIndex:idx_user_learning_daily_stats_user_date,priority:2" json:"date"`
	LearningAttempts int       `gorm:"not null;default:0" json:"learning_attempts"`
	LearningCorrect  int       `gorm:"not null;default:0" json:"learning_correct"`
	RegularRetries   int       `gorm:"not null;default:0" json:"regular_retries"`
	ArrowDuelRetries int       `gorm:"not null;default:0" json:"arrow_duel_retries"`
	PuzzlesMastered  int       `gorm:"not null;default:0" json:"puzzles_mastered"`
	TotalTimeSpent   int       `gorm:"not null;default:0" json:"total_time_spent"` // seconds
	CreatedAt        time.Time `gorm:"not null" json:"created_at"`
	UpdatedAt        time.Time `gorm:"not null" json:"updated_at"`

	// Relationships
	User User `gorm:"foreignKey:UserID;constraint:OnDelete:CASCADE" json:"-"`
}

// TableName returns the table name for UserLearningDailyStats
func (UserLearningDailyStats) TableName() string {
	return "user_learning_daily_stats"
}

// BeforeCreate sets the ID if not already set
func (ulds *UserLearningDailyStats) BeforeCreate(tx *gorm.DB) error {
	if ulds.ID == "" {
		ulds.ID = uuid.New().String()
	}
	return nil
}
