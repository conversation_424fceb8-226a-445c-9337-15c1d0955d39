package api

import (
	"context"
	"encoding/json"
	"net/http"
	"testing"

	"github.com/chessticize/chessticize-server/internal/api/testutils"
	"github.com/chessticize/chessticize-server/internal/config"
	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/chessticize/chessticize-server/internal/repository"
	"github.com/chessticize/chessticize-server/internal/repository/fake"
	"github.com/chessticize/chessticize-server/internal/service"
	firebaseEmulator "github.com/chessticize/chessticize-server/internal/testing"
	"github.com/go-chi/chi/v5"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestFirebaseTokenExchange(t *testing.T) {
	// Setup test database and repositories
	db := fake.NewDB(t)
	userRepo := fake.NewUserRepository(db)
	invitationCodeRepo := fake.NewFakeInvitationCodeRepository(db.DB)
	sessionTokenRepo := fake.NewFakeSessionTokenRepository(db.DB)
	eventRepo := fake.NewEventRepository(db.DB)

	// Setup Firebase emulator
	emulator, err := firebaseEmulator.NewFirebaseEmulator("demo-project")
	require.NoError(t, err)

	// Start Firebase emulator server
	server := emulator.StartServer("9099")
	defer func() {
		if err := server.Close(); err != nil {
			t.Logf("Failed to close server: %v", err)
		}
	}()

	// Setup services
	cfg := &config.Config{
		JWT: config.JWTConfig{
			Secret:        "test-secret",
			ExpiryMinutes: 60,
		},
		SessionToken: config.SessionTokenConfig{
			ExpiryDays: 30,
		},
		Firebase: config.FirebaseConfig{
			ProjectID:             "demo-project",
			JWKSEndpoint:          "http://localhost:9099/.well-known/jwks.json",
			SkipTokenVerification: true,
		},
	}

	authService := service.NewAuthServiceWithSessionTokens(cfg.JWT, cfg.SessionToken, sessionTokenRepo)
	eventService := service.NewEventService(eventRepo)
	firebaseAuthService := service.NewFirebaseAuthService(cfg.Firebase)

	// Setup router with proper API structure
	router := chi.NewRouter()
	router.Route("/api/v1", func(r chi.Router) {
		r.Mount("/auth", AuthRoutes(userRepo, invitationCodeRepo, authService, eventService, firebaseAuthService, cfg))
	})

	t.Run("NewUser_Success", func(t *testing.T) {
		// Generate test Firebase token with email_link provider (password is now rejected)
		userID := "firebase-user-123"
		email := testutils.RandomEmail()
		firebaseToken, err := emulator.GenerateTestTokenWithProvider(userID, email, "email_link")
		require.NoError(t, err)

		// Make request
		req := FirebaseTokenExchangeRequest{
			FirebaseToken: firebaseToken,
		}

		httpReq := testutils.MakeRequest(t, http.MethodPost, "/api/v1/auth/firebase-exchange", req)
		httpReq.Header.Set("User-Agent", "test-browser/1.0")
		resp := testutils.ExecuteRequest(t, router, httpReq)

		// Check response
		testutils.CheckResponseCode(t, http.StatusCreated, resp.Code)

		var response struct {
			Token string `json:"token"`
			User  struct {
				ID          string  `json:"id"`
				Email       string  `json:"email"`
				FirebaseUID *string `json:"firebase_uid"`
			} `json:"user"`
		}
		testutils.ParseResponseBody(t, resp, &response)

		// Verify response
		assert.NotEmpty(t, response.Token)
		assert.NotEmpty(t, response.User.ID)
		assert.Equal(t, email, response.User.Email)
		assert.NotNil(t, response.User.FirebaseUID)
		assert.Equal(t, userID, *response.User.FirebaseUID)

		// Verify user was created in database
		user, err := userRepo.GetByFirebaseUID(context.TODO(), userID)
		require.NoError(t, err)
		assert.Equal(t, email, user.Email)
		assert.Equal(t, userID, *user.FirebaseUID)

		// Verify registration event was created
		events, _, err := eventRepo.ListByUserID(context.TODO(), user.ID, repository.EventFilter{
			EventTypes: []models.EventType{models.EventTypeRegistration},
		}, 0, 10)
		require.NoError(t, err)
		require.Len(t, events, 1)

		event := events[0]
		assert.Equal(t, models.EventTypeRegistration, event.EventType)

		var eventData models.RegistrationEventData
		err = json.Unmarshal(event.EventData, &eventData)
		require.NoError(t, err)
		assert.Equal(t, "email_link", eventData.SignInType) // From Firebase token
		assert.Equal(t, "test-browser/1.0", eventData.UserAgent)
	})

	t.Run("ExistingUser_Success", func(t *testing.T) {
		// Create existing user
		existingUser := &models.User{
			Email:        testutils.RandomEmail(),
			PasswordHash: "existing-hash",
		}
		err := userRepo.Create(context.TODO(), existingUser)
		require.NoError(t, err)

		// Generate test Firebase token with same email using email_link provider
		userID := "firebase-user-456"
		firebaseToken, err := emulator.GenerateTestTokenWithProvider(userID, existingUser.Email, "email_link")
		require.NoError(t, err)

		// Make request
		req := FirebaseTokenExchangeRequest{
			FirebaseToken: firebaseToken,
		}

		httpReq := testutils.MakeRequest(t, http.MethodPost, "/api/v1/auth/firebase-exchange", req)
		httpReq.Header.Set("User-Agent", "test-browser/1.0")
		resp := testutils.ExecuteRequest(t, router, httpReq)

		// Check response (should be 200, not 201 for existing user)
		testutils.CheckResponseCode(t, http.StatusOK, resp.Code)

		var response struct {
			Token string `json:"token"`
			User  struct {
				ID          string  `json:"id"`
				Email       string  `json:"email"`
				FirebaseUID *string `json:"firebase_uid"`
			} `json:"user"`
		}
		testutils.ParseResponseBody(t, resp, &response)

		// Verify response
		assert.NotEmpty(t, response.Token)
		assert.Equal(t, existingUser.ID, response.User.ID) // Same user ID
		assert.Equal(t, existingUser.Email, response.User.Email)
		assert.NotNil(t, response.User.FirebaseUID)
		assert.Equal(t, userID, *response.User.FirebaseUID)

		// Verify user was linked in database
		user, err := userRepo.GetByID(context.TODO(), existingUser.ID)
		require.NoError(t, err)
		assert.Equal(t, userID, *user.FirebaseUID)

		// Verify sign-in event was created (not registration)
		events, _, err := eventRepo.ListByUserID(context.TODO(), user.ID, repository.EventFilter{
			EventTypes:    []models.EventType{models.EventTypeSignIn},
			EventSubTypes: []models.EventSubType{models.EventSubTypeSuccess},
		}, 0, 10)
		require.NoError(t, err)
		require.Len(t, events, 1)

		event := events[0]
		assert.Equal(t, models.EventTypeSignIn, event.EventType)
		assert.Equal(t, models.EventSubTypeSuccess, *event.EventSubType)

		var eventData models.SignInEventData
		err = json.Unmarshal(event.EventData, &eventData)
		require.NoError(t, err)
		assert.True(t, eventData.Succeeded)
		assert.Equal(t, "email_link", eventData.SignInType) // From Firebase token
		assert.Equal(t, "test-browser/1.0", eventData.UserAgent)
	})

	t.Run("InvalidToken_Failure", func(t *testing.T) {
		// Make request with invalid token
		req := FirebaseTokenExchangeRequest{
			FirebaseToken: "invalid-token",
		}

		httpReq := testutils.MakeRequest(t, http.MethodPost, "/api/v1/auth/firebase-exchange", req)
		resp := testutils.ExecuteRequest(t, router, httpReq)

		// Check response
		testutils.CheckResponseCode(t, http.StatusUnauthorized, resp.Code)
	})

	t.Run("EmptyToken_Failure", func(t *testing.T) {
		// Make request with empty token
		req := FirebaseTokenExchangeRequest{
			FirebaseToken: "",
		}

		httpReq := testutils.MakeRequest(t, http.MethodPost, "/api/v1/auth/firebase-exchange", req)
		resp := testutils.ExecuteRequest(t, router, httpReq)

		// Check response
		testutils.CheckResponseCode(t, http.StatusBadRequest, resp.Code)
	})

	t.Run("PasswordSignIn_Rejected", func(t *testing.T) {
		// Generate test Firebase token with password sign-in provider
		userID := "firebase-user-password-test"
		email := testutils.RandomEmail()
		firebaseToken, err := emulator.GenerateTestTokenWithProvider(userID, email, "password")
		require.NoError(t, err)

		// Make request
		req := FirebaseTokenExchangeRequest{
			FirebaseToken: firebaseToken,
		}

		httpReq := testutils.MakeRequest(t, http.MethodPost, "/api/v1/auth/firebase-exchange", req)
		resp := testutils.ExecuteRequest(t, router, httpReq)

		// Check response - should be rejected with 401
		testutils.CheckResponseCode(t, http.StatusUnauthorized, resp.Code)

		// Check error message
		var errorResp map[string]interface{}
		testutils.ParseResponseBody(t, resp, &errorResp)
		require.Contains(t, errorResp["error"].(string), "Password sign-in is not allowed")
	})

	t.Run("EmailLinkSignIn_Success", func(t *testing.T) {
		// Generate test Firebase token with email_link sign-in provider
		userID := "firebase-user-email-link"
		email := testutils.RandomEmail()
		firebaseToken, err := emulator.GenerateTestTokenWithProvider(userID, email, "email_link")
		require.NoError(t, err)

		// Make request
		req := FirebaseTokenExchangeRequest{
			FirebaseToken: firebaseToken,
		}

		httpReq := testutils.MakeRequest(t, http.MethodPost, "/api/v1/auth/firebase-exchange", req)
		httpReq.Header.Set("User-Agent", "test-browser/1.0")
		resp := testutils.ExecuteRequest(t, router, httpReq)

		// Check response - should be successful
		testutils.CheckResponseCode(t, http.StatusCreated, resp.Code)

		var response struct {
			Token string `json:"token"`
			User  struct {
				ID          string  `json:"id"`
				Email       string  `json:"email"`
				FirebaseUID *string `json:"firebase_uid"`
			} `json:"user"`
		}
		testutils.ParseResponseBody(t, resp, &response)

		// Verify response
		require.NotEmpty(t, response.Token)
		require.Equal(t, email, response.User.Email)
		require.NotNil(t, response.User.FirebaseUID)
		require.Equal(t, userID, *response.User.FirebaseUID)
	})

	t.Run("GoogleSignIn_Success", func(t *testing.T) {
		// Generate test Firebase token with google.com sign-in provider
		userID := "firebase-user-google"
		email := testutils.RandomEmail()
		firebaseToken, err := emulator.GenerateTestTokenWithProvider(userID, email, "google.com")
		require.NoError(t, err)

		// Make request
		req := FirebaseTokenExchangeRequest{
			FirebaseToken: firebaseToken,
		}

		httpReq := testutils.MakeRequest(t, http.MethodPost, "/api/v1/auth/firebase-exchange", req)
		httpReq.Header.Set("User-Agent", "test-browser/1.0")
		resp := testutils.ExecuteRequest(t, router, httpReq)

		// Check response - should be successful
		testutils.CheckResponseCode(t, http.StatusCreated, resp.Code)

		var response struct {
			Token string `json:"token"`
			User  struct {
				ID          string  `json:"id"`
				Email       string  `json:"email"`
				FirebaseUID *string `json:"firebase_uid"`
			} `json:"user"`
		}
		testutils.ParseResponseBody(t, resp, &response)

		// Verify response
		require.NotEmpty(t, response.Token)
		require.Equal(t, email, response.User.Email)
		require.NotNil(t, response.User.FirebaseUID)
		require.Equal(t, userID, *response.User.FirebaseUID)
	})

	t.Run("NoSessionTokenCreated_EmailLink", func(t *testing.T) {
		// Generate test Firebase token with email_link sign-in provider
		userID := "firebase-user-no-session"
		email := testutils.RandomEmail()
		firebaseToken, err := emulator.GenerateTestTokenWithProvider(userID, email, "email_link")
		require.NoError(t, err)

		// Make request
		req := FirebaseTokenExchangeRequest{
			FirebaseToken: firebaseToken,
		}

		httpReq := testutils.MakeRequest(t, http.MethodPost, "/api/v1/auth/firebase-exchange", req)
		httpReq.Header.Set("User-Agent", "test-browser/1.0")
		resp := testutils.ExecuteRequest(t, router, httpReq)

		// Check response - should be successful
		testutils.CheckResponseCode(t, http.StatusCreated, resp.Code)

		var response struct {
			Token string `json:"token"`
			User  struct {
				ID          string  `json:"id"`
				Email       string  `json:"email"`
				FirebaseUID *string `json:"firebase_uid"`
			} `json:"user"`
		}
		testutils.ParseResponseBody(t, resp, &response)

		// Verify response structure - no session_token field should be present
		require.NotEmpty(t, response.Token)
		require.NotEmpty(t, response.User.ID)

		// Verify no session tokens were created in the database for this user
		sessionTokens, err := sessionTokenRepo.ListByUserID(context.TODO(), response.User.ID)
		require.NoError(t, err)
		require.Len(t, sessionTokens, 0, "No session tokens should be created for Firebase authentication")

		// Also verify that the response doesn't contain a session_token field by checking raw JSON
		var rawResponse map[string]interface{}
		testutils.ParseResponseBody(t, resp, &rawResponse)
		_, hasSessionToken := rawResponse["session_token"]
		require.False(t, hasSessionToken, "Response should not contain session_token field")
	})

	t.Run("NoSessionTokenCreated_ExistingUser", func(t *testing.T) {
		// Create existing user first
		existingUser := &models.User{
			Email:        testutils.RandomEmail(),
			PasswordHash: "existing-hash",
		}
		err := userRepo.Create(context.TODO(), existingUser)
		require.NoError(t, err)

		// Generate test Firebase token with same email using email_link provider
		userID := "firebase-user-existing-no-session"
		firebaseToken, err := emulator.GenerateTestTokenWithProvider(userID, existingUser.Email, "email_link")
		require.NoError(t, err)

		// Make request
		req := FirebaseTokenExchangeRequest{
			FirebaseToken: firebaseToken,
		}

		httpReq := testutils.MakeRequest(t, http.MethodPost, "/api/v1/auth/firebase-exchange", req)
		httpReq.Header.Set("User-Agent", "test-browser/1.0")
		resp := testutils.ExecuteRequest(t, router, httpReq)

		// Check response - should be successful (200 for existing user)
		testutils.CheckResponseCode(t, http.StatusOK, resp.Code)

		var response struct {
			Token string `json:"token"`
			User  struct {
				ID          string  `json:"id"`
				Email       string  `json:"email"`
				FirebaseUID *string `json:"firebase_uid"`
			} `json:"user"`
		}
		testutils.ParseResponseBody(t, resp, &response)

		// Verify response structure
		require.NotEmpty(t, response.Token)
		require.Equal(t, existingUser.ID, response.User.ID)
		require.Equal(t, existingUser.Email, response.User.Email)

		// Verify no session tokens were created in the database for this user
		sessionTokens, err := sessionTokenRepo.ListByUserID(context.TODO(), response.User.ID)
		require.NoError(t, err)
		require.Len(t, sessionTokens, 0, "No session tokens should be created for Firebase authentication, even for existing users")

		// Also verify that the response doesn't contain a session_token field
		var rawResponse map[string]interface{}
		testutils.ParseResponseBody(t, resp, &rawResponse)
		_, hasSessionToken := rawResponse["session_token"]
		require.False(t, hasSessionToken, "Response should not contain session_token field")
	})
}
