# Learning Queue Design

## Overview

The learning queue system provides a spaced repetition mechanism for Lichess puzzles that users failed to solve during puzzle sprints. This is a separate queue from the existing puzzle queue (which handles user-generated puzzles) and follows a similar design pattern with adaptations for Lichess puzzles.

**Key Features:**
- <PERSON><PERSON> failed Lichess puzzles from both regular and arrow-duel sprints
- Separate from the existing user puzzle queue
- Uses the same spaced repetition algorithm as the original puzzle queue
- Supports both regular puzzle solving and arrow-duel retry modes
- Tracks daily learning statistics and retry attempts
- Integrates with existing Lichess puzzle infrastructure

## Database Schema

### LearningQueueEntry Model (GORM Auto-Migration)

Following the pattern from the existing `PuzzleQueueEntry`, the learning queue will use GORM auto-migration:

```go
// LearningQueueEntry represents a failed Lichess puzzle in the user's learning queue
type LearningQueueEntry struct {
    ID                  string    `gorm:"type:varchar(36);primarykey" json:"id"`
    UserID              string    `gorm:"type:varchar(36);not null;index:idx_learning_queue_user_due,priority:1;uniqueIndex:idx_learning_queue_user_puzzle,priority:1;index:idx_learning_queue_user_type_due,priority:1" json:"user_id"`
    LichessPuzzleID     string    `gorm:"type:varchar(10);not null;uniqueIndex:idx_learning_queue_user_puzzle,priority:2" json:"lichess_puzzle_id"`
    FailedAttemptType   string    `gorm:"type:varchar(20);not null;index:idx_learning_queue_user_type_due,priority:2" json:"failed_attempt_type"` // "regular" or "arrow_duel"
    DueAt               time.Time `gorm:"not null;index:idx_learning_queue_user_due,priority:2;index:idx_learning_queue_user_type_due,priority:3" json:"due_at"`
    AttemptsSinceAdded  int       `gorm:"not null;default:0" json:"attempts_since_added"`
    ConsecutiveCorrect  int       `gorm:"not null;default:0" json:"consecutive_correct"`
    OriginalSprintID    string    `gorm:"type:varchar(36);not null" json:"original_sprint_id"` // Track which sprint the failure came from
    CreatedAt           time.Time `gorm:"not null" json:"created_at"`
    UpdatedAt           time.Time `gorm:"not null" json:"updated_at"`

    // Relationships
    LichessPuzzle LichessPuzzle `gorm:"foreignKey:LichessPuzzleID;constraint:OnDelete:CASCADE" json:"-"`
    User          User          `gorm:"foreignKey:UserID;constraint:OnDelete:CASCADE" json:"-"`
    Sprint        Sprint        `gorm:"foreignKey:OriginalSprintID;constraint:OnDelete:CASCADE" json:"-"`
}

// TableName returns the table name for LearningQueueEntry
func (LearningQueueEntry) TableName() string {
    return "learning_queue"
}
```

**GORM Indexes Created:**
- `idx_learning_queue_user_due` on `(user_id, due_at)` - for fetching due puzzles
- `idx_learning_queue_user_puzzle` on `(user_id, lichess_puzzle_id)` - unique constraint and quick removal
- `idx_learning_queue_user_type_due` on `(user_id, failed_attempt_type, due_at)` - for filtering by attempt type

### Key Design Decisions

1. **Lichess Puzzles Only**: Only failed Lichess puzzles from sprints are queued
2. **Attempt Type Tracking**: Tracks whether the original failure was in regular or arrow-duel mode
3. **Sprint Tracking**: Records which sprint the failure originated from for analytics
4. **Unique Constraint**: `(user_id, lichess_puzzle_id)` prevents duplicate queue entries
5. **User-Centric Indexing**: All indexes include `user_id` as first column for efficient user-based queries
6. **Cascade Delete**: Automatically removes queue entries when users, puzzles, or sprints are deleted
7. **Same Spaced Repetition**: Uses identical scheduling logic as the original puzzle queue

## Integration with Sprint System

### Automatic Queue Addition

When a sprint ends (via `EndSprint` API), the system will:

1. **Identify Failed Puzzles**: Query `sprint_puzzle_attempts` for incorrect attempts
2. **Filter Eligible Puzzles**: Only add puzzles that aren't already in the learning queue
3. **Determine Attempt Type**: Check if the sprint was regular or arrow-duel based
4. **Add to Learning Queue**: Create `LearningQueueEntry` records with `due_at = NOW()` (due today)

### Sprint Service Integration

```go
// Add to SprintService.EndSprint method
func (s *SprintService) EndSprint(ctx context.Context, sprintID string, userID string) (*SprintEndResponse, error) {
    // ... existing logic ...
    
    // After sprint completion, add failed puzzles to learning queue
    err = s.addFailedPuzzlesToLearningQueue(ctx, userID, sprintID)
    if err != nil {
        // Log error but don't fail sprint completion
        log := logger.FromContext(ctx)
        log.Error().Err(err).Str("sprint_id", sprintID).Msg("Failed to add puzzles to learning queue")
    }
    
    return response, nil
}

func (s *SprintService) addFailedPuzzlesToLearningQueue(ctx context.Context, userID, sprintID string) error {
    // Get failed puzzle attempts from this sprint
    failedAttempts, err := s.sprintPuzzleAttemptRepo.GetFailedAttempts(ctx, sprintID, userID)
    if err != nil {
        return fmt.Errorf("failed to get failed attempts: %w", err)
    }
    
    // Convert to learning queue entries
    var learningEntries []models.LearningQueueEntry
    for _, attempt := range failedAttempts {
        // Check if already in learning queue
        exists, err := s.learningQueueRepo.ExistsInQueue(ctx, userID, attempt.LichessPuzzleID)
        if err != nil || exists {
            continue // Skip if error or already queued
        }
        
        entry := models.LearningQueueEntry{
            UserID:             userID,
            LichessPuzzleID:    attempt.LichessPuzzleID,
            FailedAttemptType:  attempt.AttemptType, // "regular" or "arrow_duel"
            DueAt:              time.Now(), // Due today
            OriginalSprintID:   sprintID,
        }
        learningEntries = append(learningEntries, entry)
    }
    
    // Add to learning queue
    if len(learningEntries) > 0 {
        _, err = s.learningQueueRepo.AddPuzzlesToQueue(ctx, userID, learningEntries)
        return err
    }
    
    return nil
}
```

## API Endpoints

### 1. Fetch Due Learning Puzzles

**Endpoint**: `GET /api/learning-queue/due`

**Query Parameters**:
- `limit` (optional, default: 10, max: 50)
- `attempt_type` (optional: `"regular"` or `"arrow_duel"`, both if not specified)

**Response**:
```json
{
    "puzzles": [
        {
            "queue_id": "uuid",
            "lichess_puzzle_id": "00008",
            "failed_attempt_type": "arrow_duel",
            "due_at": "2024-01-15T10:00:00Z",
            "attempts_since_added": 1,
            "consecutive_correct": 0,
            "original_sprint_id": "sprint-uuid",
            "puzzle_data": {
                "id": "00008",
                "fen": "r6k/pp2r2p/4Rp1Q/3p4/8/1N1P2R1/PqP2bPP/7K b - - 0 24",
                "moves": ["f2g3", "e6e7", "b2b1", "b3c1", "b1c1", "h6c1"],
                "rating": 1798,
                "themes": ["crushing", "hangingPiece", "long", "middlegame"]
            }
        }
    ],
    "total_due": 15
}
```

### 2. Get Learning Queue Stats

**Endpoint**: `GET /api/learning-queue/stats`

**Response**:
```json
{
    "total_queued": 25,
    "due_today": 8,
    "regular_puzzles": 15,
    "arrow_duel_puzzles": 10,
    "daily_retries_today": 5,
    "daily_retries_this_week": 23,
    "mastery_rate": 0.65
}
```

## Learning Attempt Integration

### Enhanced Lichess Puzzle Attempt API

The existing Lichess puzzle attempt API will be enhanced to support learning queue updates:

**Endpoint**: `POST /api/v1/users/me/lichess-puzzles/{puzzleID}/attempts`

**Enhanced Request**:
```json
{
    "moves": ["f2g3"],
    "solved": true,
    "time_spent": 45,
    "attempt_type": "regular",
    "is_learning_attempt": true,  // NEW: Flag to enable learning queue scheduling
    "candidate_moves": ["f2g3", "f2h4"],  // For arrow-duel retries
    "chosen_move": "f2g3"  // For arrow-duel retries
}
```

### Learning Queue Update Logic

When `is_learning_attempt: true` is provided:

1. **Check Learning Queue**: Look up the puzzle in the user's learning queue
2. **Update Attempt Counters**: Increment `attempts_since_added`
3. **Apply Spaced Repetition**: Use the same algorithm as the original puzzle queue

```
IF attempt is CORRECT:
    consecutive_correct++
    
    IF consecutive_correct >= 5:
        // Remove from queue - mastered
        DELETE from learning_queue
    ELSE:
        // Schedule for future review
        CASE consecutive_correct:
            1: due_at = NOW() + 2 days
            2: due_at = NOW() + 4 days  
            3: due_at = NOW() + 7 days
            4: due_at = NOW() + 15 days
ELSE (attempt is INCORRECT):
    consecutive_correct = 0
    due_at = NOW() + 24 hours  // Try again tomorrow
```

## Daily Learning Statistics

### UserLearningDailyStats Model

Track daily learning activity for analytics and motivation:

```go
type UserLearningDailyStats struct {
    ID                    string    `gorm:"type:varchar(36);primarykey" json:"id"`
    UserID                string    `gorm:"type:varchar(36);not null;uniqueIndex:idx_user_learning_daily_stats_user_date,priority:1" json:"user_id"`
    Date                  time.Time `gorm:"type:date;not null;uniqueIndex:idx_user_learning_daily_stats_user_date,priority:2" json:"date"`
    LearningAttempts      int       `gorm:"not null;default:0" json:"learning_attempts"`
    LearningCorrect       int       `gorm:"not null;default:0" json:"learning_correct"`
    RegularRetries        int       `gorm:"not null;default:0" json:"regular_retries"`
    ArrowDuelRetries      int       `gorm:"not null;default:0" json:"arrow_duel_retries"`
    PuzzlesMastered       int       `gorm:"not null;default:0" json:"puzzles_mastered"`
    TotalTimeSpent        int       `gorm:"not null;default:0" json:"total_time_spent"` // seconds
    CreatedAt             time.Time `gorm:"not null" json:"created_at"`
    UpdatedAt             time.Time `gorm:"not null" json:"updated_at"`

    // Relationships
    User User `gorm:"foreignKey:UserID;constraint:OnDelete:CASCADE" json:"-"`
}
```

## Repository Interface

### ILearningQueueRepository

```go
type ILearningQueueRepository interface {
    // Add puzzles to learning queue
    AddPuzzlesToQueue(ctx context.Context, userID string, puzzles []LearningQueueEntry) (int, error)
    
    // Get due puzzles for user
    GetDuePuzzles(ctx context.Context, userID string, attemptType *string, limit int) ([]LearningQueueItem, error)

    // Update puzzle queue entry after attempt
    UpdateAfterAttempt(ctx context.Context, userID, lichessPuzzleID string, wasCorrect bool) error

    // Check if puzzle exists in queue
    ExistsInQueue(ctx context.Context, userID, lichessPuzzleID string) (bool, error)
    
    // Remove puzzle from queue (when mastered)
    RemoveFromQueue(ctx context.Context, userID, lichessPuzzleID string) error
    
    // Get queue stats for user
    GetQueueStats(ctx context.Context, userID string) (*LearningQueueStats, error)
}
```

## Implementation Phases

### Phase 1: Core Infrastructure
1. Create `LearningQueueEntry` model with GORM auto-migration
2. Implement `LearningQueueRepository` with basic CRUD operations
3. Add unit tests for repository operations
4. Create database migration for `learning_queue` table

### Phase 2: Sprint Integration
1. Add failed puzzle detection to `SprintService.EndSprint`
2. Implement automatic learning queue population
3. Add `ExistsInQueue` method to prevent duplicates
4. Test sprint-to-learning-queue flow

### Phase 3: API Endpoints
1. Implement `GET /api/learning-queue/due` endpoint
2. Implement `GET /api/learning-queue/stats` endpoint
3. Create API handlers and request/response models
4. Add integration tests for endpoints

### Phase 4: Learning Attempt Integration
1. Enhance Lichess puzzle attempt API with `is_learning_attempt` flag
2. Implement learning queue update logic in attempt handlers
3. Add spaced repetition scheduling
4. Test end-to-end learning flow

### Phase 5: Daily Statistics
1. Create `UserLearningDailyStats` model
2. Implement daily stats tracking via database triggers
3. Add learning analytics endpoints
4. Create performance monitoring and cleanup jobs

## Testing Strategy

### Unit Tests
- Repository operations (CRUD, filtering, scheduling)
- Service layer logic (spaced repetition algorithm)
- Model validation and constraints

### Integration Tests
- Sprint-to-learning-queue integration
- API endpoint functionality
- Database constraint enforcement

### End-to-End Tests
- Complete learning workflow from sprint failure to mastery
- Spaced repetition scheduling accuracy
- Performance with large datasets

## Performance Considerations

1. **Indexing**: Proper indexes on `(user_id, due_at)` for efficient due puzzle queries
2. **Batch Operations**: Support adding multiple puzzles in single transaction
3. **Pagination**: Limit query results to prevent memory issues
4. **Cleanup**: Periodic cleanup of old mastered entries (optional)

## Security Considerations

1. **User Isolation**: All queries filtered by user ID to prevent data leakage
2. **Input Validation**: Validate puzzle IDs and types before queue operations
3. **Rate Limiting**: Prevent abuse of learning queue endpoints
4. **Authorization**: Ensure users can only access their own learning queue data
